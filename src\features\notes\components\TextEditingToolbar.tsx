import React from 'react';
import { 
  <PERSON>Bold, 
  FiItalic, 
  FiUnderline, 
  FiCode, 
  FiList, 
  FiLink,
  FiType,
  FiAlignLeft,
  FiAlignCenter,
  FiAlignRight
} from 'react-icons/fi';

interface TextEditingToolbarProps {
  onFormatText: (format: string) => void;
  selectedText: string;
  isVisible: boolean;
}

const TextEditingToolbar: React.FC<TextEditingToolbarProps> = ({
  onFormatText,
  selectedText,
  isVisible
}) => {
  if (!isVisible) return null;

  const formatButtons = [
    { icon: FiBold, format: 'bold', title: 'Bold (Ctrl+B)', shortcut: '**text**' },
    { icon: FiItalic, format: 'italic', title: 'Italic (Ctrl+I)', shortcut: '*text*' },
    { icon: FiUnderline, format: 'underline', title: 'Underline', shortcut: '__text__' },
    { icon: FiCode, format: 'code', title: 'Inline Code', shortcut: '`text`' },
    { icon: FiList, format: 'list', title: 'Bullet List', shortcut: '- item' },
    { icon: FiLink, format: 'link', title: 'Link', shortcut: '[text](url)' },
  ];

  return (
    <div className="absolute z-50 bg-[#1a1d23] border border-gray-600 rounded-lg shadow-lg p-2 flex gap-1">
      {formatButtons.map(({ icon: Icon, format, title, shortcut }) => (
        <button
          key={format}
          onClick={() => onFormatText(format)}
          className="p-2 hover:bg-gray-700 rounded text-gray-300 hover:text-white transition-colors"
          title={`${title} - ${shortcut}`}
        >
          <Icon size={16} />
        </button>
      ))}
      <div className="w-px bg-gray-600 mx-1" />
      <button
        onClick={() => onFormatText('heading')}
        className="p-2 hover:bg-gray-700 rounded text-gray-300 hover:text-white transition-colors"
        title="Heading - # text"
      >
        <FiType size={16} />
      </button>
    </div>
  );
};

export default TextEditingToolbar;
