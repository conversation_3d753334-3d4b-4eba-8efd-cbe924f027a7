import React, { useRef, useState, useCallback, useImperativeHandle, forwardRef } from 'react';
import TextEditingToolbar from './TextEditingToolbar';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  onKeyDown?: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
  onFocus?: () => void;
  placeholder?: string;
  className?: string;
  autoFocus?: boolean;
}

export interface RichTextEditorRef {
  focus: () => void;
  setCursorToEnd: () => void;
}

const RichTextEditor = forwardRef<RichTextEditorRef, RichTextEditorProps>(({
  value,
  onChange,
  onKeyDown,
  onFocus,
  placeholder = "(empty)",
  className = "",
  autoFocus = false
}, ref) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [showToolbar, setShowToolbar] = useState(false);
  const [toolbarPosition, setToolbarPosition] = useState({ top: 0, left: 0 });
  const [selectedText, setSelectedText] = useState('');

  // Expose focus methods to parent
  useImperativeHandle(ref, () => ({
    focus: () => {
      if (textareaRef.current) {
        textareaRef.current.focus();
      }
    },
    setCursorToEnd: () => {
      if (textareaRef.current) {
        const length = textareaRef.current.value.length;
        textareaRef.current.setSelectionRange(length, length);
      }
    }
  }), []);

  const handleSelectionChange = useCallback(() => {
    if (!textareaRef.current) return;

    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    
    if (start !== end) {
      const selected = value.substring(start, end);
      setSelectedText(selected);
      
      // Calculate toolbar position
      const rect = textarea.getBoundingClientRect();
      setToolbarPosition({
        top: rect.top - 50,
        left: rect.left + (rect.width / 2) - 100
      });
      setShowToolbar(true);
    } else {
      setShowToolbar(false);
      setSelectedText('');
    }
  }, [value]);

  const handleMouseUp = () => {
    setTimeout(handleSelectionChange, 10);
  };

  const handleKeyUp = (e: React.KeyboardEvent) => {
    if (e.key === 'Shift' || e.key === 'Control' || e.key === 'Alt') {
      handleSelectionChange();
    }
  };

  const formatText = useCallback((format: string) => {
    if (!textareaRef.current) return;

    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.substring(start, end);

    let formattedText = '';
    let newCursorPos = start;

    switch (format) {
      case 'bold':
        formattedText = `**${selectedText}**`;
        newCursorPos = selectedText ? start + formattedText.length : start + 2;
        break;
      case 'italic':
        formattedText = `*${selectedText}*`;
        newCursorPos = selectedText ? start + formattedText.length : start + 1;
        break;
      case 'underline':
        formattedText = `__${selectedText}__`;
        newCursorPos = selectedText ? start + formattedText.length : start + 2;
        break;
      case 'code':
        formattedText = `\`${selectedText}\``;
        newCursorPos = selectedText ? start + formattedText.length : start + 1;
        break;
      case 'link':
        formattedText = selectedText 
          ? `[${selectedText}](url)` 
          : '[text](url)';
        newCursorPos = selectedText ? start + formattedText.length - 4 : start + 1;
        break;
      case 'list':
        formattedText = selectedText 
          ? `- ${selectedText}` 
          : '- ';
        newCursorPos = start + formattedText.length;
        break;
      case 'heading':
        formattedText = selectedText 
          ? `# ${selectedText}` 
          : '# ';
        newCursorPos = start + formattedText.length;
        break;
      default:
        return;
    }

    const newValue = value.substring(0, start) + formattedText + value.substring(end);
    onChange(newValue);

    // Restore cursor position
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus();
        textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
      }
    }, 0);

    setShowToolbar(false);
  }, [value, onChange]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Handle keyboard shortcuts for formatting (only when Ctrl/Cmd is pressed)
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'b':
          e.preventDefault();
          formatText('bold');
          return; // Don't pass to parent
        case 'i':
          e.preventDefault();
          formatText('italic');
          return; // Don't pass to parent
        case 'k':
          e.preventDefault();
          formatText('link');
          return; // Don't pass to parent
        case '`':
          e.preventDefault();
          formatText('code');
          return; // Don't pass to parent
      }
    }

    // For navigation keys (Enter, Tab, Arrow keys), always pass to parent
    // These are handled by the note navigation logic
    const navigationKeys = ['Enter', 'Tab', 'ArrowUp', 'ArrowDown', 'Backspace'];
    if (navigationKeys.includes(e.key)) {
      if (onKeyDown) {
        onKeyDown(e);
      }
      return;
    }

    // For other keys, let the textarea handle them normally
    // Only call parent onKeyDown if it's not a formatting shortcut
    if (onKeyDown && !(e.ctrlKey || e.metaKey)) {
      onKeyDown(e);
    }
  }, [formatText, onKeyDown]);

  return (
    <div className="relative">
      <textarea
        ref={textareaRef}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onKeyDown={handleKeyDown}
        onKeyUp={handleKeyUp}
        onMouseUp={handleMouseUp}
        onFocus={onFocus}
        placeholder={placeholder}
        autoFocus={autoFocus}
        draggable={false}
        className={`bg-transparent text-gray-100 border-none outline-none text-base font-medium py-1 w-full resize-none ${className}`}
        style={{ minHeight: 24, resize: 'none' }}
        rows={1}
        onInput={(e) => {
          // Auto-resize textarea
          const target = e.target as HTMLTextAreaElement;
          target.style.height = 'auto';
          target.style.height = target.scrollHeight + 'px';
        }}
      />
      
      {showToolbar && (
        <div
          style={{
            position: 'fixed',
            top: toolbarPosition.top,
            left: toolbarPosition.left,
            zIndex: 1000
          }}
        >
          <TextEditingToolbar
            onFormatText={formatText}
            selectedText={selectedText}
            isVisible={showToolbar}
          />
        </div>
      )}
    </div>
  );
});

RichTextEditor.displayName = 'RichTextEditor';

export default RichTextEditor;
